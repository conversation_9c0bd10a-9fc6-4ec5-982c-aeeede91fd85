/**
 * Video Booking System JavaScript
 *
 * @package VideoBooking
 */

jQuery(document).ready(function ($) {
  "use strict";

  // Check if videoBooking is defined
  if (typeof videoBooking === "undefined") {
    console.error(
      "videoBooking is not defined. Make sure the script is properly localized."
    );
    return;
  }

  // Video Booking Object
  const VideoBooking = {
    // Initialize
    init: function () {
      console.log("VideoBooking initialized");
      this.bindEvents();
      this.initCart();
      this.initSecurity();
    },

    // Bind events
    bindEvents: function () {
      // Add to cart
      $(document).on("click", ".add-to-cart", this.addToCart);

      // Remove from cart
      $(document).on("click", ".remove-from-cart", this.removeFromCart);

      // Apply coupon
      $(document).on("click", ".apply-coupon", this.applyCoupon);

      // Checkout form
      $(document).on("submit", ".video-checkout-form", this.processCheckout);

      // Update cart display
      $(document).on("cart-updated", this.updateCartDisplay);
    },

    // Add video to cart
    addToCart: function (e) {
      e.preventDefault();

      const $button = $(this);
      const videoId = $button.data("video-id");
      const isEarlyBird = $button.data("early-bird") || false;

      $button
        .prop("disabled", true)
        .html('<span class="spinner"></span> Adding...');

      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: {
          action: "video_add_to_cart",
          nonce: videoBooking.nonce,
          video_id: videoId,
          is_early_bird: isEarlyBird,
        },
        success: function (response) {
          if (response.success) {
            $button.html("Added to Cart").addClass("btn-success");
            VideoBooking.showMessage("Video added to cart!", "success");
            $(document).trigger("cart-updated");

            // Reset button after 2 seconds
            setTimeout(function () {
              $button
                .html("Add to Cart")
                .removeClass("btn-success")
                .prop("disabled", false);
            }, 2000);
          } else {
            VideoBooking.showMessage(
              response.data || "Failed to add video to cart",
              "error"
            );
            $button.html("Add to Cart").prop("disabled", false);
          }
        },
        error: function () {
          VideoBooking.showMessage("Network error. Please try again.", "error");
          $button.html("Add to Cart").prop("disabled", false);
        },
      });
    },

    // Remove video from cart
    removeFromCart: function (e) {
      e.preventDefault();

      const $button = $(this);
      const videoId = $button.data("video-id");

      $button.prop("disabled", true);

      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: {
          action: "video_remove_from_cart",
          nonce: videoBooking.nonce,
          video_id: videoId,
        },
        success: function (response) {
          if (response.success) {
            $button.closest(".cart-item").fadeOut(300, function () {
              $(this).remove();
              $(document).trigger("cart-updated");
            });
            VideoBooking.showMessage("Video removed from cart", "success");
          } else {
            VideoBooking.showMessage(
              response.data || "Failed to remove video",
              "error"
            );
            $button.prop("disabled", false);
          }
        },
        error: function () {
          VideoBooking.showMessage("Network error. Please try again.", "error");
          $button.prop("disabled", false);
        },
      });
    },

    // Apply coupon code
    applyCoupon: function (e) {
      e.preventDefault();

      const $button = $(this);
      const $input = $(".coupon-input");
      const couponCode = $input.val().trim();

      if (!couponCode) {
        VideoBooking.showMessage("Please enter a coupon code", "error");
        return;
      }

      $button
        .prop("disabled", true)
        .html('<span class="spinner"></span> Applying...');

      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: {
          action: "video_apply_coupon",
          nonce: videoBooking.nonce,
          coupon_code: couponCode,
        },
        success: function (response) {
          if (response.success) {
            VideoBooking.showMessage("Coupon applied successfully!", "success");
            $(document).trigger("cart-updated");
            $input.prop("disabled", true);
            $button.html("Applied").addClass("btn-success");
          } else {
            VideoBooking.showMessage(
              response.data || "Invalid coupon code",
              "error"
            );
            $button.html("Apply").prop("disabled", false);
          }
        },
        error: function () {
          VideoBooking.showMessage("Network error. Please try again.", "error");
          $button.html("Apply").prop("disabled", false);
        },
      });
    },

    // Process checkout
    processCheckout: function (e) {
      e.preventDefault();

      const $form = $(this);
      const $submitButton = $form.find(".checkout-submit");

      // Validate form
      if (!VideoBooking.validateCheckoutForm($form)) {
        return;
      }

      $submitButton
        .prop("disabled", true)
        .html('<span class="spinner"></span> Processing...');

      const formData = $form.serialize();
      formData += "&action=video_create_order&nonce=" + videoBooking.nonce;

      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: formData,
        success: function (response) {
          if (response.success) {
            if (response.data.is_free_order) {
              // Free order - redirect to success page
              window.location.href = response.data.redirect_url;
            } else {
              // Paid order - initiate Razorpay payment
              VideoBooking.initiatePayment(response.data);
            }
          } else {
            VideoBooking.showMessage(
              response.data || "Checkout failed",
              "error"
            );
            $submitButton.html("Complete Purchase").prop("disabled", false);
          }
        },
        error: function () {
          VideoBooking.showMessage("Network error. Please try again.", "error");
          $submitButton.html("Complete Purchase").prop("disabled", false);
        },
      });
    },

    // Initiate Razorpay payment
    initiatePayment: function (orderData) {
      // Check if Razorpay is loaded
      if (typeof Razorpay === "undefined") {
        VideoBooking.showMessage(
          "Payment gateway not available. Please refresh the page and try again.",
          "error"
        );
        $(".checkout-submit").html("Complete Purchase").prop("disabled", false);
        return;
      }

      const options = {
        key: videoBooking.razorpay_key,
        amount: orderData.amount,
        currency: "INR",
        name: "Cosmic Tree Foundation",
        description: "Video Workshop Purchase",
        order_id: orderData.razorpay_order_id,
        handler: function (response) {
          VideoBooking.verifyPayment(response, orderData.order_id);
        },
        prefill: {
          name: orderData.customer_name,
          email: orderData.customer_email,
          contact: orderData.customer_phone,
        },
        theme: {
          color: "#2c5aa0",
        },
        modal: {
          ondismiss: function () {
            $(".checkout-submit")
              .html("Complete Purchase")
              .prop("disabled", false);
          },
        },
      };

      const rzp = new Razorpay(options);
      rzp.open();
    },

    // Verify payment
    verifyPayment: function (paymentResponse, orderId) {
      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: {
          action: "video_verify_payment",
          nonce: videoBooking.nonce,
          order_id: orderId,
          razorpay_payment_id: paymentResponse.razorpay_payment_id,
          razorpay_order_id: paymentResponse.razorpay_order_id,
          razorpay_signature: paymentResponse.razorpay_signature,
        },
        success: function (response) {
          if (response.success) {
            window.location.href = response.data.redirect_url;
          } else {
            VideoBooking.showMessage("Payment verification failed", "error");
            $(".checkout-submit")
              .html("Complete Purchase")
              .prop("disabled", false);
          }
        },
        error: function () {
          VideoBooking.showMessage("Payment verification failed", "error");
          $(".checkout-submit")
            .html("Complete Purchase")
            .prop("disabled", false);
        },
      });
    },

    // Validate checkout form
    validateCheckoutForm: function ($form) {
      let isValid = true;

      $form.find(".form-error").remove();

      // Required fields
      $form.find("[required]").each(function () {
        const $field = $(this);
        const value = $field.val().trim();

        if (!value) {
          VideoBooking.showFieldError($field, "This field is required");
          isValid = false;
        }
      });

      // Email validation
      const $email = $form.find('input[type="email"]');
      if ($email.length && $email.val()) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test($email.val())) {
          VideoBooking.showFieldError(
            $email,
            "Please enter a valid email address"
          );
          isValid = false;
        }
      }

      // Phone validation
      const $phone = $form.find('input[name="customer_phone"]');
      if ($phone.length && $phone.val()) {
        const phoneRegex = /^[0-9]{10}$/;
        if (!phoneRegex.test($phone.val().replace(/\D/g, ""))) {
          VideoBooking.showFieldError(
            $phone,
            "Please enter a valid 10-digit phone number"
          );
          isValid = false;
        }
      }

      return isValid;
    },

    // Show field error
    showFieldError: function ($field, message) {
      const $error = $('<div class="form-error">' + message + "</div>");
      $field.after($error);
      $field.addClass("error");
    },

    // Initialize cart
    initCart: function () {
      this.updateCartDisplay();
    },

    // Update cart display
    updateCartDisplay: function () {
      $.ajax({
        url: videoBooking.ajax_url,
        type: "POST",
        data: {
          action: "video_get_cart",
          nonce: videoBooking.nonce,
        },
        success: function (response) {
          if (response.success) {
            $(".cart-count").text(response.data.count);
            $(".cart-total-amount").text("₹" + response.data.total);
          }
        },
      });
    },

    // Initialize security features
    initSecurity: function () {
      // Disable right-click on video elements
      $(document).on("contextmenu", "video", function (e) {
        e.preventDefault();
        return false;
      });

      // Disable F12, Ctrl+Shift+I, Ctrl+U
      $(document).on("keydown", function (e) {
        if (
          e.keyCode === 123 || // F12
          (e.ctrlKey && e.shiftKey && e.keyCode === 73) || // Ctrl+Shift+I
          (e.ctrlKey && e.keyCode === 85)
        ) {
          // Ctrl+U
          e.preventDefault();
          return false;
        }
      });

      // Disable text selection on video containers
      $(".video-player-container").css({
        "-webkit-user-select": "none",
        "-moz-user-select": "none",
        "-ms-user-select": "none",
        "user-select": "none",
      });
    },

    // Show message
    showMessage: function (message, type) {
      const $message = $(
        '<div class="video-message video-message-' +
          type +
          '">' +
          message +
          "</div>"
      );

      // Remove existing messages
      $(".video-message").remove();

      // Add new message
      $("body").prepend($message);

      // Auto-hide after 5 seconds
      setTimeout(function () {
        $message.fadeOut(300, function () {
          $(this).remove();
        });
      }, 5000);
    },
  };

  // Initialize Video Booking
  VideoBooking.init();

  // Make VideoBooking globally available
  window.VideoBooking = VideoBooking;
});

// Message styles (injected via JavaScript)
jQuery(document).ready(function ($) {
  const messageStyles = `
        <style>
        .video-message {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 6px;
            color: white;
            font-weight: bold;
            z-index: 9999;
            max-width: 300px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        .video-message-success {
            background: #28a745;
        }
        .video-message-error {
            background: #dc3545;
        }
        .video-message-warning {
            background: #ffc107;
            color: #212529;
        }
        .video-message-info {
            background: #17a2b8;
        }
        </style>
    `;
  $("head").append(messageStyles);

  // Initialize VideoBooking
  VideoBooking.init();
});
