<?php

/**
 * Authentication System for Video Booking
 * 
 * @package VideoBooking
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Video Booking Authentication Class
 */
class VideoBookingAuth
{

    /**
     * Initialize authentication system
     */
    public static function init()
    {
        // Add rewrite rules for auth pages
        add_action('init', array(__CLASS__, 'add_auth_rewrite_rules'));

        // Handle auth page templates
        add_action('template_redirect', array(__CLASS__, 'handle_auth_templates'));

        // Add AJAX handlers
        add_action('wp_ajax_nopriv_video_login', array(__CLASS__, 'handle_login'));
        add_action('wp_ajax_nopriv_video_register', array(__CLASS__, 'handle_register'));
        add_action('wp_ajax_video_logout', array(__CLASS__, 'handle_logout'));
        add_action('wp_ajax_nopriv_video_logout', array(__CLASS__, 'handle_logout'));
        add_action('wp_ajax_video_update_profile', array(__CLASS__, 'handle_profile_update'));
        add_action('wp_ajax_video_change_password', array(__CLASS__, 'handle_password_change'));

        // Enqueue auth scripts
        add_action('wp_enqueue_scripts', array(__CLASS__, 'enqueue_auth_scripts'));

        // Add login/logout links to menu
        add_filter('wp_nav_menu_items', array(__CLASS__, 'add_auth_menu_items'), 10, 2);
    }

    /**
     * Add rewrite rules for authentication pages
     */
    public static function add_auth_rewrite_rules()
    {
        // Login page - override WordPress default
        add_rewrite_rule(
            '^login/?$',
            'index.php?video_auth=login',
            'top'
        );

        // Register page - override WordPress default
        add_rewrite_rule(
            '^register/?$',
            'index.php?video_auth=register',
            'top'
        );

        // My account page
        add_rewrite_rule(
            '^my-account/?$',
            'index.php?video_auth=account',
            'top'
        );

        // Add query var
        add_rewrite_tag('%video_auth%', '([^&]+)');

        // Disable WordPress default login redirects
        add_action('init', array(__CLASS__, 'disable_wp_login_redirects'), 1);

        // Flush rewrite rules if needed
        if (get_option('video_auth_rewrite_flushed') !== VIDEO_BOOKING_VERSION) {
            flush_rewrite_rules();
            update_option('video_auth_rewrite_flushed', VIDEO_BOOKING_VERSION);
        }
    }

    /**
     * Disable WordPress login redirects
     */
    public static function disable_wp_login_redirects()
    {
        // Prevent WordPress from redirecting /login to wp-login.php
        remove_action('template_redirect', 'wp_redirect_admin_locations', 1000);

        // Handle login URL redirects manually
        add_action('template_redirect', array(__CLASS__, 'handle_login_redirects'), 1);
    }

    /**
     * Handle login URL redirects
     */
    public static function handle_login_redirects()
    {
        global $wp;

        // Check if this is a login or register request
        if (isset($wp->request)) {
            $request = trim($wp->request, '/');

            if ($request === 'login' || $request === 'register') {
                // Don't let WordPress handle this
                return;
            }
        }

        // Check for wp-login.php access and redirect to custom login
        if (strpos($_SERVER['REQUEST_URI'], 'wp-login.php') !== false && !is_admin()) {
            $redirect_to = isset($_GET['redirect_to']) ? $_GET['redirect_to'] : '';
            $login_url = home_url('/login');

            if ($redirect_to) {
                $login_url = add_query_arg('redirect_to', urlencode($redirect_to), $login_url);
            }

            wp_redirect($login_url);
            exit;
        }
    }

    /**
     * Handle authentication templates
     */
    public static function handle_auth_templates()
    {
        $auth_page = get_query_var('video_auth');

        if (!$auth_page) {
            return;
        }

        $templates = array(
            'login' => 'auth/login.php',
            'register' => 'auth/register.php',
            'account' => 'auth/account.php'
        );

        if (!isset($templates[$auth_page])) {
            return;
        }

        // Redirect logged-in users away from login/register pages
        if (in_array($auth_page, array('login', 'register')) && is_user_logged_in()) {
            wp_redirect(home_url('/my-account'));
            exit;
        }

        // Redirect non-logged-in users to login from account page
        if ($auth_page === 'account' && !is_user_logged_in()) {
            wp_redirect(home_url('/login'));
            exit;
        }

        $template_file = VIDEO_BOOKING_PATH . 'templates/' . $templates[$auth_page];

        if (file_exists($template_file)) {
            include $template_file;
            exit;
        }
    }

    /**
     * Handle login AJAX request
     */
    public static function handle_login()
    {
        check_ajax_referer('video_auth_nonce', 'nonce');

        $username = sanitize_user($_POST['username']);
        $password = $_POST['password'];
        $remember = isset($_POST['remember']) ? true : false;

        if (empty($username) || empty($password)) {
            wp_send_json_error('Please fill in all fields');
        }

        // Authenticate using custom user table
        $user = self::authenticate_user($username, $password);

        if (!$user) {
            wp_send_json_error('Invalid username or password');
        }

        if ($user->status !== 'active') {
            wp_send_json_error('Your account has been deactivated. Please contact support.');
        }

        // Set custom session
        self::set_user_session($user, $remember);

        // Update last login
        self::update_last_login($user->id);

        wp_send_json_success(array(
            'message' => 'Login successful!',
            'redirect_url' => home_url('/my-recordings')
        ));
    }

    /**
     * Handle registration AJAX request
     */
    public static function handle_register()
    {
        check_ajax_referer('video_auth_nonce', 'nonce');

        $username = sanitize_user($_POST['username']);
        $email = sanitize_email($_POST['email']);
        $password = $_POST['password'];
        $confirm_password = $_POST['confirm_password'];
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);

        // Validation
        if (empty($username) || empty($email) || empty($password)) {
            wp_send_json_error('Please fill in all required fields');
        }

        if (!is_email($email)) {
            wp_send_json_error('Please enter a valid email address');
        }

        if ($password !== $confirm_password) {
            wp_send_json_error('Passwords do not match');
        }

        if (strlen($password) < 6) {
            wp_send_json_error('Password must be at least 6 characters long');
        }

        // Check if username or email already exists in custom table
        if (self::username_exists($username)) {
            wp_send_json_error('Username already exists');
        }

        if (self::email_exists($email)) {
            wp_send_json_error('Email address is already registered');
        }

        // Create user in custom table
        $user_id = self::create_user($username, $email, $password, $first_name, $last_name);

        if (!$user_id) {
            wp_send_json_error('Failed to create user account');
        }

        // Get the created user
        $user = self::get_user_by_id($user_id);

        // Auto-login the user
        self::set_user_session($user, false);

        // Send welcome email
        self::send_welcome_email($user);

        wp_send_json_success(array(
            'message' => 'Registration successful! Welcome to our community.',
            'redirect_url' => home_url('/my-recordings')
        ));
    }

    /**
     * Handle logout AJAX request
     */
    public static function handle_logout()
    {
        check_ajax_referer('video_auth_nonce', 'nonce');

        self::logout_user();

        wp_send_json_success(array(
            'message' => 'Logged out successfully',
            'redirect_url' => home_url()
        ));
    }

    /**
     * Handle profile update AJAX request
     */
    public static function handle_profile_update()
    {
        check_ajax_referer('video_auth_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error('Please log in to update your profile');
        }

        $user_id = get_current_user_id();
        $display_name = sanitize_text_field($_POST['display_name']);
        $first_name = sanitize_text_field($_POST['first_name']);
        $last_name = sanitize_text_field($_POST['last_name']);
        $user_email = sanitize_email($_POST['user_email']);

        // Validation
        if (empty($display_name) || empty($user_email)) {
            wp_send_json_error('Display name and email are required');
        }

        if (!is_email($user_email)) {
            wp_send_json_error('Please enter a valid email address');
        }

        // Check if email is already taken by another user
        $existing_user = get_user_by('email', $user_email);
        if ($existing_user && $existing_user->ID !== $user_id) {
            wp_send_json_error('This email address is already registered to another account');
        }

        // Update user
        $result = wp_update_user(array(
            'ID' => $user_id,
            'display_name' => $display_name,
            'first_name' => $first_name,
            'last_name' => $last_name,
            'user_email' => $user_email
        ));

        if (is_wp_error($result)) {
            wp_send_json_error($result->get_error_message());
        }

        wp_send_json_success('Profile updated successfully');
    }

    /**
     * Handle password change AJAX request
     */
    public static function handle_password_change()
    {
        check_ajax_referer('video_auth_nonce', 'nonce');

        if (!is_user_logged_in()) {
            wp_send_json_error('Please log in to change your password');
        }

        $user_id = get_current_user_id();
        $current_password = $_POST['current_password'];
        $new_password = $_POST['new_password'];
        $confirm_password = $_POST['confirm_password'];

        // Validation
        if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
            wp_send_json_error('All password fields are required');
        }

        if ($new_password !== $confirm_password) {
            wp_send_json_error('New passwords do not match');
        }

        if (strlen($new_password) < 6) {
            wp_send_json_error('New password must be at least 6 characters long');
        }

        // Verify current password
        $user = get_user_by('id', $user_id);
        if (!wp_check_password($current_password, $user->user_pass, $user_id)) {
            wp_send_json_error('Current password is incorrect');
        }

        // Update password
        wp_set_password($new_password, $user_id);

        wp_send_json_success('Password changed successfully');
    }

    /**
     * Send welcome email to new users
     */
    private static function send_welcome_email($user)
    {
        if (!$user || !isset($user->email)) {
            return false;
        }

        $subject = 'Welcome to ' . get_bloginfo('name') . '!';

        $message = "
        <h2>Welcome to " . get_bloginfo('name') . "!</h2>

        <p>Hi {$user->display_name},</p>

        <p>Thank you for creating an account with us! You can now:</p>

        <ul>
            <li>Browse and purchase workshop recordings</li>
            <li>Access your purchased videos anytime</li>
            <li>Manage your account settings</li>
        </ul>

        <p><a href='" . home_url('/workshop-recordings') . "' style='background: #2c5aa0; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;'>Browse Workshop Recordings</a></p>

        <p>If you have any questions, feel free to contact us at " . get_option('admin_email') . "</p>

        <p>Best regards,<br>The " . get_bloginfo('name') . " Team</p>
        ";

        $headers = array(
            'Content-Type: text/html; charset=UTF-8',
            'From: ' . get_bloginfo('name') . ' <' . get_option('admin_email') . '>'
        );

        return wp_mail($user->email, $subject, $message, $headers);
    }

    /**
     * Enqueue authentication scripts
     */
    public static function enqueue_auth_scripts()
    {
        $auth_page = get_query_var('video_auth');

        if ($auth_page || is_page() || is_front_page()) {
            wp_enqueue_script(
                'video-auth',
                VIDEO_BOOKING_URL . 'assets/js/video-auth.js',
                array('jquery'),
                VIDEO_BOOKING_VERSION,
                true
            );

            wp_localize_script('video-auth', 'videoAuth', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('video_auth_nonce'),
                'login_url' => home_url('/login'),
                'register_url' => home_url('/register'),
                'account_url' => home_url('/my-account'),
                'is_logged_in' => is_user_logged_in(),
                'current_user' => is_user_logged_in() ? wp_get_current_user()->display_name : ''
            ));
        }
    }

    /**
     * Add login/logout links to navigation menu
     */
    public static function add_auth_menu_items($items, $args)
    {
        // Only add to primary menu
        if ($args->theme_location !== 'primary') {
            return $items;
        }

        if (is_user_logged_in()) {
            $current_user = wp_get_current_user();
            $items .= '<li class="menu-item menu-item-account"><a href="' . home_url('/my-recordings') . '">My Recordings</a></li>';
            $items .= '<li class="menu-item menu-item-logout"><a href="#" id="logout-link">Logout (' . esc_html($current_user->display_name) . ')</a></li>';
        } else {
            $items .= '<li class="menu-item menu-item-login"><a href="' . home_url('/login') . '">Login</a></li>';
            $items .= '<li class="menu-item menu-item-register"><a href="' . home_url('/register') . '">Register</a></li>';
        }

        return $items;
    }

    /**
     * Get login URL with redirect
     */
    public static function get_login_url($redirect_to = '')
    {
        $login_url = home_url('/login');

        if ($redirect_to) {
            $login_url = add_query_arg('redirect_to', urlencode($redirect_to), $login_url);
        }

        return $login_url;
    }

    /**
     * Get register URL with redirect
     */
    public static function get_register_url($redirect_to = '')
    {
        $register_url = home_url('/register');

        if ($redirect_to) {
            $register_url = add_query_arg('redirect_to', urlencode($redirect_to), $register_url);
        }

        return $register_url;
    }

    /**
     * Authenticate user against custom user table
     */
    private static function authenticate_user($username, $password)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        // Find user by username or email
        $user = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE (username = %s OR email = %s) AND status = 'active'",
            $username,
            $username
        ));

        if (!$user) {
            return false;
        }

        // Verify password
        if (!wp_check_password($password, $user->password)) {
            return false;
        }

        return $user;
    }

    /**
     * Set user session
     */
    private static function set_user_session($user, $remember = false)
    {
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }

        // Set session data
        $_SESSION['video_booking_user_id'] = $user->id;
        $_SESSION['video_booking_user_email'] = $user->email;
        $_SESSION['video_booking_user_name'] = $user->display_name ?: $user->username;

        // Set cookie for remember me
        if ($remember) {
            $expire = time() + (30 * 24 * 60 * 60); // 30 days
            setcookie('video_booking_remember', $user->id, $expire, '/');
        }
    }

    /**
     * Update last login time
     */
    private static function update_last_login($user_id)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        $wpdb->update(
            $users_table,
            array('last_login' => current_time('mysql')),
            array('id' => $user_id),
            array('%s'),
            array('%d')
        );
    }

    /**
     * Get current logged in user
     */
    public static function get_current_user()
    {
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }

        // Check session first
        if (isset($_SESSION['video_booking_user_id'])) {
            return self::get_user_by_id($_SESSION['video_booking_user_id']);
        }

        // Check remember me cookie
        if (isset($_COOKIE['video_booking_remember'])) {
            $user = self::get_user_by_id($_COOKIE['video_booking_remember']);
            if ($user && $user->status === 'active') {
                self::set_user_session($user, true);
                return $user;
            }
        }

        return false;
    }

    /**
     * Get user by ID
     */
    private static function get_user_by_id($user_id)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        return $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM $users_table WHERE id = %d AND status = 'active'",
            $user_id
        ));
    }

    /**
     * Check if user is logged in
     */
    public static function is_user_logged_in()
    {
        return self::get_current_user() !== false;
    }

    /**
     * Logout user
     */
    public static function logout_user()
    {
        // Start session if not already started
        if (!session_id()) {
            session_start();
        }

        // Clear session
        unset($_SESSION['video_booking_user_id']);
        unset($_SESSION['video_booking_user_email']);
        unset($_SESSION['video_booking_user_name']);

        // Clear remember me cookie
        if (isset($_COOKIE['video_booking_remember'])) {
            setcookie('video_booking_remember', '', time() - 3600, '/');
        }

        // Destroy session if no other data
        if (empty($_SESSION)) {
            session_destroy();
        }
    }

    /**
     * Check if username exists in custom table
     */
    private static function username_exists($username)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $users_table WHERE username = %s",
            $username
        ));

        return $count > 0;
    }

    /**
     * Check if email exists in custom table
     */
    private static function email_exists($email)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $users_table WHERE email = %s",
            $email
        ));

        return $count > 0;
    }

    /**
     * Create user in custom table
     */
    private static function create_user($username, $email, $password, $first_name, $last_name)
    {
        global $wpdb;

        $users_table = $wpdb->prefix . 'video_booking_users';

        $display_name = trim($first_name . ' ' . $last_name);
        if (empty($display_name)) {
            $display_name = $username;
        }

        $result = $wpdb->insert(
            $users_table,
            array(
                'username' => $username,
                'email' => $email,
                'password' => wp_hash_password($password),
                'first_name' => $first_name,
                'last_name' => $last_name,
                'display_name' => $display_name,
                'status' => 'active',
                'email_verified' => 0
            ),
            array('%s', '%s', '%s', '%s', '%s', '%s', '%s', '%d')
        );

        return $result ? $wpdb->insert_id : false;
    }
}

// Initialize authentication system
VideoBookingAuth::init();
